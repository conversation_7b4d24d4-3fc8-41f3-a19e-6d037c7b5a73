<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/items.xsd">
	<item id="13307" name="Color Title (Event)" type="EtcItem">
		<!-- A dimensional item. An item which can be used to change the color of a character's title. Upon leaving a clan, the color title disappears, just as the regular one does (for everyone except <PERSON><PERSON>) -->
		<set name="icon" val="icon.color_name_i00" />
		<set name="default_action" val="NICK_COLOR" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="PAPER" />
		<set name="weight" val="5" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_freightable" val="true" />
		<set name="handler" val="NicknameColor" />
	</item>
	<item id="13340" name="Agathion - Love (Event)" additionalName="7-day" type="Armor">
		<!-- Available for 7 days. Summons Agathion - Love. -->
		<set name="icon" val="icon.etc_rbracelet_aga_agit_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="lbracelet" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="SILVER" />
		<set name="weight" val="30" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_premium" val="true" />
		<set name="time" val="10080" />
		<set name="is_destroyable" val="false" />
		<skills>
			<skill id="5763" level="1" /> <!-- Wink -->
			<skill id="3267" level="1" type="ON_UNEQUIP" /> <!-- Seal Agathion -->
		</skills>
	</item>
	<item id="13369" name="Love Agathion Pack (Event)" type="EtcItem">
		<!-- Double-click to obtain Agathion - Love (7-day). -->
		<set name="icon" val="icon.etc_pi_gift_box_i02" />
		<set name="default_action" val="PEEL" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="FISH" />
		<set name="weight" val="100" />
		<set name="is_tradable" val="false" />
		<set name="is_dropable" val="false" />
		<set name="is_sellable" val="false" />
		<set name="is_stackable" val="true" />
		<set name="is_premium" val="true" />
		<set name="handler" val="ExtractableItems" />
		<capsuled_items>
			<item id="13340" min="1" max="1" chance="100" /> <!-- Agathion Love (Event) - 7 days -->
		</capsuled_items>
	</item>
</list>
