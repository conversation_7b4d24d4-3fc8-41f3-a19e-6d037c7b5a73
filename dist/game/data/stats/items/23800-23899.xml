<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/items.xsd">
	<item id="23877" name="Samurai Hair Accessory" type="Armor">
		<!-- Samurai Hair Accessory. -->
		<set name="icon" val="BranchSys3.iconArmar.g_co_japan_general_hair_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="hairall" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="CLOTH" />
		<set name="is_sellable" val="false" />
	</item>
	<item id="23879" name="Samurai Outfit" type="Armor">
		<!-- Samurai's Outfit. -->
		<set name="icon" val="BranchSys3.iconArmar.g_co_japan_general_i00" />
		<set name="default_action" val="EQUIP" />
		<set name="bodypart" val="alldress" />
		<set name="immediate_effect" val="true" />
		<set name="material" val="CLOTH" />
		<set name="is_sellable" val="false" />
	</item>
</list>
