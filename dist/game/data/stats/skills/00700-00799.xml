<?xml version="1.0" encoding="UTF-8"?>
<list xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../xsd/skills.xsd">
	<skill id="700" toLevel="1" name="Divine Healer Group Heal">
		<!-- Recovers party member's HP by 400 power and recovers +27 HP per sec. for 15 sec. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>5</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>LIFE_FORCE_OTHERS</abnormalType>
		<activateRate>0</activateRate>
		<affectObject>FRIEND</affectObject>
		<affectRange>1000</affectRange>
		<affectScope>PARTY</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>661</effectPoint>
		<hitTime>7000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>252</mpConsume>
		<reuseDelay>6000</reuseDelay>
		<effects>
			<effect name="HealOverTime">
				<power>27</power>
				<ticks>1</ticks>
			</effect>
			<effect name="Heal">
				<power>400</power>
			</effect>
		</effects>
	</skill>
	<skill id="701" toLevel="1" name="Divine Healer Resurrection">
		<!-- Resurrects a corpse. Restores XP by about 70 percent additionally. -->
		<icon>icon.skill_transform_etc</icon>
		<operateType>A1</operateType>
		<targetType>PC_BODY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>400</castRange>
		<effectPoint>661</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>6000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>252</mpConsume>
		<reuseDelay>30000</reuseDelay>
		<conditions>
			<condition name="OpResurrection" />
		</conditions>
		<effects>
			<effect name="Resurrection">
				<power>70</power>
			</effect>
		</effects>
	</skill>
	<skill id="702" toLevel="1" name="Divine Healer Cleanse">
		<!-- Cancel all the debuffs of the target. -->
		<icon>icon.skill_transform_etc</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>600</castRange>
		<effectPoint>676</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>59</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="DispelByCategory">
				<slot>DEBUFF</slot>
				<rate>100</rate>
				<max>10</max>
			</effect>
		</effects>
	</skill>
	<skill id="703" toLevel="1" name="Sacrifice Healer">
		<!-- Regenerate party member's HP and MP by sacrificing yourself. Usable only when MP is under 10 percent. -->
		<icon>icon.skill_transform_etc</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>FRIEND</affectObject>
		<affectRange>300</affectRange>
		<affectScope>PARTY</affectScope>
		<effectPoint>676</effectPoint>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<reuseDelay>1800000</reuseDelay>
		<staticReuse>true</staticReuse>
		<conditions>
			<condition name="RemainMpPer">
				<amount>10</amount>
				<percentType>LESS</percentType>
			</condition>
		</conditions>
		<effects>
			<effect name="HealPercent">
				<power>100</power>
			</effect>
			<effect name="ManaHealPercent">
				<power>100</power>
			</effect>
		</effects>
		<selfEffects>
			<effect name="CallSkill">
				<skillId>5602</skillId> <!-- Transform Sacrifice -->
				<skillLevel>1</skillLevel>
			</effect>
		</selfEffects>
	</skill>
	<skill id="704" toLevel="1" name="Divine Enchanter Water Spirit">
		<!-- For 2 min., receives help from a great spirit to get a party member's chance of prominent magic damage +2, MP Recovery Bonus +20%, P. Atk. +10%, P. Def. +20%, Atk. Spd. +20%, M. Atk. +20%, M. Def. +20%, Casting Spd. +20%, and Debuff Resistance +10%. Speed -20% and Skill MP Consumption -5%. Consumes 10 Spirit Ore. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>120</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>400</castRange>
		<effectPoint>669</effectPoint>
		<effectRange>600</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<itemConsumeCount>10</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>73</mpConsume>
		<reuseDelay>4000</reuseDelay>
		<conditions>
			<condition name="TargetMyParty">
				<includeMe>true</includeMe>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttack">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>-20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MpRegen">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>-10</amount>
				<slot>DEBUFF</slot>
			</effect>
			<effect name="MagicCriticalRate">
				<amount>2</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicMpCost">
				<amount>-5</amount>
				<mode>PER</mode>
				<magicType>1</magicType>
			</effect>
		</effects>
	</skill>
	<skill id="705" toLevel="1" name="Divine Enchanter Fire Spirit">
		<!-- For 2 min., receives help from a great spirit to get a selected party member's Max MP +20%, HP Recovery Bonus +20%, M. Critical Rate +2%, P. Critical Damage +20%, P. Atk. +10%, P. Def. +20%, Atk. Spd. +20%, M. Atk. +20%, M. Def. +20%, Casting Spd. +20%, and Debuff Resistance +10%. Speed -20%. Consumes 10 Spirit Ore. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>120</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>400</castRange>
		<effectPoint>669</effectPoint>
		<effectRange>600</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<itemConsumeCount>10</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>73</mpConsume>
		<reuseDelay>4000</reuseDelay>
		<conditions>
			<condition name="TargetMyParty">
				<includeMe>true</includeMe>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttack">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>-20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Accuracy">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MaxMp">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="HpRegen">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>-10</amount>
				<slot>DEBUFF</slot>
			</effect>
			<effect name="MagicCriticalRate">
				<amount>2</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="706" toLevel="1" name="Divine Enchanter Wind Spirit">
		<!-- For 2 min., receives help from a great spirit to get a selected party member's Max HP +20%, P. Critical Rate +20%, M. Critical Damage +20%, P. Atk. +10%, P. Def. +20%, Atk. Spd. +20%, M. Atk. +20%, M. Def. +20%, Casting Spd. +20%, and Debuff Resistance +10%. Speed -20%. Has a chance of recovering 5% of damage inflicted on the enemy as HP. Consumes 10 Spirit Ore. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>120</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>400</castRange>
		<effectPoint>669</effectPoint>
		<effectRange>600</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<itemConsumeCount>10</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>73</mpConsume>
		<reuseDelay>4000</reuseDelay>
		<conditions>
			<condition name="TargetMyParty">
				<includeMe>true</includeMe>
			</condition>
		</conditions>
		<effects>
			<effect name="MaxHp">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="VampiricAttack">
				<amount>5</amount>
				<chance>80</chance>
			</effect>
			<effect name="PhysicalAttack">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttack">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>-20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>-10</amount>
				<slot>DEBUFF</slot>
			</effect>
			<effect name="CriticalRate">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicCriticalRate">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="707" toLevel="1" name="Divine Enchanter Hero Spirit">
		<!-- For 2 min., receives help from a great spirit to get a selected party member's Max MP +20%, M. Critical Rate +2, P. Critical Damage +20%, P. Atk. +10%, P. Def. +20%, Atk. Spd. +20%, M. Atk. +20%, M. Def. +20%, Casting Spd. +20%, and Debuff Resistance +10%. Speed -20%. Consumes 10 Spirit Ore. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>120</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>400</castRange>
		<effectPoint>669</effectPoint>
		<effectRange>600</effectRange>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<itemConsumeCount>10</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>73</mpConsume>
		<reuseDelay>4000</reuseDelay>
		<conditions>
			<condition name="TargetMyParty">
				<includeMe>true</includeMe>
			</condition>
		</conditions>
		<effects>
			<effect name="MaxHp">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttack">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttack">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>-20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Accuracy">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>-10</amount>
				<slot>DEBUFF</slot>
			</effect>
			<effect name="MagicCriticalRate">
				<amount>2</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="708" toLevel="1" name="Divine Enchanter Mass Binding">
		<!-- Momentarily apply Hold to surrounding enemies. Additional Hold is not available while effects last. -->
		<icon>icon.skill_transform_debuff</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>ROOT_MAGICALLY</abnormalType>
		<abnormalVisualEffect>ROOT</abnormalVisualEffect>
		<activateRate>40</activateRate>
		<affectLimit>10-10</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<basicProperty>MAGIC</basicProperty>
		<effectPoint>-676</effectPoint>
		<hitTime>4000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>30</lvlBonusRate>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>109</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<trait>HOLD</trait>
		<effects>
			<effect name="Root" />
			<effect name="DefenceTrait">
				<HOLD>100</HOLD>
			</effect>
		</effects>
	</skill>
	<skill id="709" toLevel="1" name="Sacrifice Enchanter">
		<!-- Increase all the party member's abilities by sacrificing yourself. Usable only when MP is under 10 percent. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>TRANSFORM_SCRIFICE</abnormalType>
		<affectObject>FRIEND</affectObject>
		<affectRange>300</affectRange>
		<affectScope>PARTY</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>1</effectPoint>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<reuseDelay>1800000</reuseDelay>
		<staticReuse>true</staticReuse>
		<conditions>
			<condition name="RemainMpPer">
				<amount>10</amount>
				<percentType>LESS</percentType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalAttack">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttack">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Accuracy">
				<amount>2</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalRate">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
		</effects>
		<selfEffects>
			<effect name="CallSkill">
				<skillId>5602</skillId> <!-- Transform Sacrifice -->
				<skillLevel>1</skillLevel>
			</effect>
		</selfEffects>
	</skill>
	<skill id="710" toLevel="1" name="Divine Summoner Summon Divine Beast">
		<!-- Summons a Holy Wild Beat. Requires 2 A-grade Crystals. -->
		<icon>icon.skill_transform_etc</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<hitTime>15000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<itemConsumeCount>2</itemConsumeCount>
		<itemConsumeId>1461</itemConsumeId> <!-- Crystal (A-Grade) -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>117</mpConsume>
		<mpInitialConsume>28</mpInitialConsume>
		<reuseDelay>5000</reuseDelay>
		<conditions>
			<condition name="CanSummon" />
		</conditions>
		<effects>
			<effect name="Summon">
				<npcId>14870</npcId> <!-- Divine Beast -->
				<consumeItemId>1461</consumeItemId> <!-- Crystal (A-grade) -->
				<consumeItemCount>1</consumeItemCount>
				<expMultiplier>0.7</expMultiplier>
			</effect>
		</effects>
	</skill>
	<skill id="711" toLevel="1" name="Divine Summoner Transfer Pain">
		<!-- Transfers some of the player's damage to the summoner. Consumes MP continuously. -->
		<icon>icon.skill_transform_etc</icon>
		<operateType>T</operateType>
		<targetType>NONE</targetType>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpInitialConsume>72</mpInitialConsume>
		<effects>
			<effect name="MpConsumePerLevel">
				<power>0.4</power>
				<ticks>1</ticks>
			</effect>
			<effect name="TransferDamageToSummon">
				<amount>50</amount>
			</effect>
		</effects>
	</skill>
	<skill id="712" toLevel="1" name="Divine Summoner Final Servitor">
		<!-- Temporarily causes the summoner to be possessed with the spirit of an ancient hero. Consumes 20 Spirit Ore. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<targetType>SUMMON</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>300</abnormalTime>
		<abnormalType>MULTI_BUFF</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>400</castRange>
		<effectPoint>669</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<itemConsumeCount>20</itemConsumeCount>
		<itemConsumeId>3031</itemConsumeId> <!-- Spirit Ore -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>57</mpConsume>
		<mpInitialConsume>15</mpInitialConsume>
		<reuseDelay>60000</reuseDelay>
		<effects>
			<effect name="MaxHp">
				<amount>20</amount>
				<mode>PER</mode>
				<heal>true</heal>
			</effect>
			<effect name="PhysicalAttack">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttack">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Speed">
				<amount>-20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="Accuracy">
				<amount>4</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>-20</amount>
				<slot>DEBUFF</slot>
			</effect>
			<effect name="CriticalRate">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="713" toLevel="1" name="Divine Summoner Servitor Hill">
		<!-- Restores the servitor's HP by 991 power. -->
		<icon>icon.skill_transform_etc</icon>
		<operateType>A1</operateType>
		<targetType>SUMMON</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>600</castRange>
		<effectPoint>1</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>127</mpConsume>
		<reuseDelay>3000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>991</power>
			</effect>
		</effects>
	</skill>
	<skill id="714" toLevel="1" name="Sacrifice Summoner">
		<!-- Increase the party member's p. critical abilities by sacrificing yourself. Usable only when MP is under 10 percent. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>TRANSFORM_SCRIFICE</abnormalType>
		<affectObject>FRIEND</affectObject>
		<affectRange>300</affectRange>
		<affectScope>PARTY</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>1</effectPoint>
		<hitTime>2500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<reuseDelay>1800000</reuseDelay>
		<staticReuse>true</staticReuse>
		<conditions>
			<condition name="RemainMpPer">
				<amount>10</amount>
				<percentType>LESS</percentType>
			</condition>
		</conditions>
		<effects>
			<effect name="CriticalDamage">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalRate">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
		</effects>
		<selfEffects>
			<effect name="CallSkill">
				<skillId>5602</skillId> <!-- Transform Sacrifice -->
				<skillLevel>1</skillLevel>
			</effect>
		</selfEffects>
	</skill>
	<skill id="715" toLevel="4" name="Zaken Energy Drain">
		<!-- Exhausts enemy's HP/ MP and absorbs some of HP. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>100</affectRange>
		<affectScope>RANGE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>600</castRange>
		<coolTime>2000</coolTime>
		<effectPoint>
			<value level="1">-307</value>
			<value level="2">-323</value>
			<value level="3">-335</value>
			<value level="4">-342</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">65</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">65</value>
			<value level="3">69</value>
			<value level="4">72</value>
		</mpConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="HpDrain">
				<power>
					<value level="1">121</value>
					<value level="2">133</value>
					<value level="3">145</value>
					<value level="4">160</value>
				</power>
				<percentage>80</percentage>
			</effect>
			<effect name="Mp">
				<amount>
					<value level="1">-274</value>
					<value level="2">-348</value>
					<value level="3">-422</value>
					<value level="4">-496</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="716" toLevel="4" name="Zaken Hold">
		<!-- Momentarily inflicts Hold on the enemy. Additional Hold is not available while effects last. -->
		<icon>icon.skill_transform_debuff</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>ROOT_MAGICALLY</abnormalType>
		<abnormalVisualEffect>ROOT</abnormalVisualEffect>
		<activateRate>95</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>MAGIC</basicProperty>
		<castRange>800</castRange>
		<coolTime>2000</coolTime>
		<effectPoint>
			<value level="1">-307</value>
			<value level="2">-323</value>
			<value level="3">-335</value>
			<value level="4">-342</value>
		</effectPoint>
		<effectRange>1300</effectRange>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">65</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">65</value>
			<value level="3">69</value>
			<value level="4">72</value>
		</mpConsume>
		<reuseDelay>2000</reuseDelay>
		<trait>HOLD</trait>
		<effects>
			<effect name="Root" />
		</effects>
	</skill>
	<skill id="717" toLevel="4" name="Zaken Concentrated Attack">
		<!-- Level 1: Focuses on one specific place and attacks with 2751 power. Over-hit. -->
		<!-- Level 2: Focuses on one specific place and attacks with 3252 power. Over-hit. -->
		<!-- Level 3: Focuses on one specific place and attacks with 3752 power. Over-hit. -->
		<!-- Level 4: Focuses on one specific place and attacks with 4224 power. Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>50</castRange>
		<coolTime>900</coolTime>
		<effectPoint>
			<value level="1">-307</value>
			<value level="2">-323</value>
			<value level="3">-335</value>
			<value level="4">-342</value>
		</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>2100</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">65</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">65</value>
			<value level="3">69</value>
			<value level="4">72</value>
		</mpConsume>
		<reuseDelay>3000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">2751</value>
					<value level="2">3252</value>
					<value level="3">3752</value>
					<value level="4">4224</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="718" toLevel="4" name="Zaken Dancing Sword">
		<!-- Level 1: Uses dancing swords to attack all at once. Power 1651 Over-hit. -->
		<!-- Level 2: Uses dancing swords to attack all at once. Power 1951 Over-hit. -->
		<!-- Level 3: Uses dancing swords to attack all at once. Power 2251 Over-hit. -->
		<!-- Level 4: Uses dancing swords to attack all at once. Power 2534 Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>100</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<coolTime>1500</coolTime>
		<effectPoint>
			<value level="1">-307</value>
			<value level="2">-323</value>
			<value level="3">-335</value>
			<value level="4">-342</value>
		</effectPoint>
		<hitTime>1500</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">65</value>
			<value level="2">70</value>
			<value level="3">75</value>
			<value level="4">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">60</value>
			<value level="2">65</value>
			<value level="3">69</value>
			<value level="4">72</value>
		</mpConsume>
		<reuseDelay>15000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">1651</value>
					<value level="2">1951</value>
					<value level="3">2251</value>
					<value level="4">2534</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="719" toLevel="1" name="Zaken Vampiric Rage">
		<!-- Temporarily recovers as HP a part of the damage inflicted on the enemy with a certain probability. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>4</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>VAMPIRIC_ATTACK</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>400</castRange>
		<effectPoint>604</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>65</magicLevel>
		<mpConsume>48</mpConsume>
		<mpInitialConsume>12</mpInitialConsume>
		<reuseDelay>2000</reuseDelay>
		<effects>
			<effect name="VampiricAttack">
				<amount>15</amount>
				<chance>80</chance>
			</effect>
		</effects>
	</skill>
	<skill id="720" toLevel="2" name="Anakim Holy Light Burst">
		<!-- Level 1: Concentrates to deal heavy damage. Power 3189 Over-hit. -->
		<!-- Level 2: Concentrates to deal heavy damage. Power 3590 Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>HOLY</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>
			<value level="1">-335</value>
			<value level="2">-342</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1080</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">75</value>
			<value level="2">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">69</value>
			<value level="2">72</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>2000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">3189</value>
					<value level="2">3590</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="721" toLevel="2" name="Anakim Energy Attack">
		<!-- Level 1: Concentrates energy and fires it. Power 186 -->
		<!-- Level 2: Concentrates energy and fires it. Power 197 -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>RANGE</affectScope>
		<attributeType>HOLY</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>700</castRange>
		<effectPoint>
			<value level="1">-335</value>
			<value level="2">-342</value>
		</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">75</value>
			<value level="2">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">69</value>
			<value level="2">72</value>
		</mpConsume>
		<reuseDelay>2000</reuseDelay>
		<effects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">186</value>
					<value level="2">197</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="722" toLevel="2" name="Anakim Divine Beam">
		<!-- Level 1: Deals damage with a beam of light. Power 186 -->
		<!-- Level 2: Deals damage with a beam of light. Power 197 -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>RANGE</affectScope>
		<attributeType>HOLY</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>700</castRange>
		<effectPoint>
			<value level="1">-335</value>
			<value level="2">-342</value>
		</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">75</value>
			<value level="2">80</value>
		</magicLevel>
		<mpConsume>
			<value level="1">69</value>
			<value level="2">72</value>
		</mpConsume>
		<reuseDelay>3000</reuseDelay>
		<effects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">186</value>
					<value level="2">197</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="723" toLevel="1" name="Anakim Sunshine">
		<!-- Instantly recovers a party member's HP. Power 1340. -->
		<icon>icon.skill_transform_etc</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>FRIEND</affectObject>
		<affectRange>1000</affectRange>
		<affectScope>PARTY</affectScope>
		<effectPoint>789</effectPoint>
		<hitCancelTime>0.2</hitCancelTime>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>75</magicLevel>
		<mpConsume>20</mpConsume>
		<mpInitialConsume>5</mpInitialConsume>
		<reuseDelay>3000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>1340</power>
			</effect>
		</effects>
	</skill>
	<skill id="724" toLevel="1" name="Anakim Cleanse">
		<!-- Cancel all of your debuffs. -->
		<icon>icon.skill_transform_etc</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectScope>SINGLE</affectScope>
		<effectPoint>659</effectPoint>
		<hitTime>1500</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>75</magicLevel>
		<mpConsume>20</mpConsume>
		<mpInitialConsume>5</mpInitialConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="DispelByCategory">
				<slot>DEBUFF</slot>
				<rate>100</rate>
				<max>10</max>
			</effect>
		</effects>
	</skill>
	<skill id="725" toLevel="2" name="Venom Power Smash">
		<!-- Level 1: Unleashes a powerful strike. Power 2626 Over-hit. -->
		<!-- Level 2: Unleashes a powerful strike. Power 2957. Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-342</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">75</value>
			<value level="2">80</value>
		</magicLevel>
		<mpConsume>88</mpConsume>
		<reuseDelay>2000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">2626</value>
					<value level="2">2957</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="726" toLevel="2" name="Venom Sonic Storm">
		<!-- Level 1: Attacks the surrounding area by detonating the Momentum. Power 2626 -->
		<!-- Level 2: Attacks the surrounding area by detonating the Momentum. Power 2957 -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>150</affectRange>
		<affectScope>RANGE</affectScope>
		<castRange>500</castRange>
		<effectPoint>-342</effectPoint>
		<effectRange>1000</effectRange>
		<hitTime>1900</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">75</value>
			<value level="2">80</value>
		</magicLevel>
		<mpConsume>88</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">2626</value>
					<value level="2">2957</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="727" toLevel="1" name="Venom Disillusion">
		<!-- Temporarily P. Atk. +8%. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>PA_UP</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>138</effectPoint>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>75</magicLevel>
		<mpConsume>28</mpConsume>
		<mpInitialConsume>7</mpInitialConsume>
		<reuseDelay>2000</reuseDelay>
		<effects>
			<effect name="PhysicalAttack">
				<amount>8</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="728" toLevel="1" name="Gordon Beast Attack">
		<!-- Inflicts a beast attack. Over-hit is possible. Power 2957. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-342</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>88</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>2957</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="729" toLevel="1" name="Gordon Sword Stab">
		<!-- Attacks the enemies in front of the player. Over-hit is possible. Power 2957. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>150</affectRange>
		<affectScope>RANGE</affectScope>
		<castRange>500</castRange>
		<effectPoint>-342</effectPoint>
		<effectRange>1000</effectRange>
		<hitTime>1900</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>88</mpConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>1774</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="730" toLevel="1" name="Gordon Press">
		<!-- Instantly nearby enemies' Speed/ Atk. Spd./ Casting Spd. -23%. -->
		<icon>icon.skill_transform_debuff</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>ATTACK_TIME_UP</abnormalType>
		<activateRate>60</activateRate>
		<affectLimit>10-10</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<basicProperty>MAGIC</basicProperty>
		<effectPoint>-1</effectPoint>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>20</lvlBonusRate>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>87</mpConsume>
		<mpInitialConsume>22</mpInitialConsume>
		<reuseDelay>2000</reuseDelay>
		<effects>
			<effect name="Speed">
				<amount>-23</amount>
				<mode>PER</mode>
			</effect>
			<effect name="PhysicalAttackSpeed">
				<amount>-23</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalAttackSpeed">
				<amount>-23</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="731" toLevel="1" name="Ranku Dark Explosion">
		<!-- Attack by detonating the devil's energy. Power 162. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>300</affectRange>
		<affectScope>RANGE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>900</castRange>
		<effectPoint>-100</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>3300</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>112</mpConsume>
		<reuseDelay>15000</reuseDelay>
		<effects>
			<effect name="MagicalDamage">
				<power>162</power>
			</effect>
		</effects>
	</skill>
	<skill id="732" toLevel="1" name="Ranku Stun Attack">
		<!-- Deals damage and shock simultaneously. Momentarily inflicts Stun on the enemy. Over-hit is possible. 1 power. -->
		<icon>icon.skill_transform_debuff</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>9</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>50</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>-342</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1080</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>20</lvlBonusRate>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>19</mpConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>3000</reuseDelay>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>1479</power>
				<overHit>true</overHit>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="733" toLevel="1" name="Kechi Double Cutter">
		<!-- Attacks by crossing a sword. Over-hit is possible. Power 2957. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-342</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>88</mpConsume>
		<reuseDelay>2000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>2957</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="734" toLevel="1" name="Kechi Air Blade">
		<!-- Strikes a distant enemy by using Momentum. Critical hit is possible. Over-hit is possible. Power 1812. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>600</castRange>
		<effectPoint>-342</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>1900</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>56</mpConsume>
		<reuseDelay>3000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>1812</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="735" toLevel="1" name="Devil Spinning Weapon">
		<!-- Attack surrounding enemies by swinging the weapon. Power 162. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<effectPoint>-342</effectPoint>
		<hitTime>3300</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>87</mpConsume>
		<mpInitialConsume>22</mpInitialConsume>
		<reuseDelay>11000</reuseDelay>
		<effects>
			<effect name="MagicalDamage">
				<power>162</power>
			</effect>
		</effects>
	</skill>
	<skill id="736" toLevel="1" name="Devil Seed">
		<!-- Momentarily decreases HP. Serious damage will be caused by the fully grown seed if not canceled. -->
		<icon>icon.skill_transform_debuff</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>9</abnormalLevel>
		<abnormalTime>15</abnormalTime>
		<abnormalType>DARK_SEED</abnormalType>
		<abnormalVisualEffect>DOT_BLEEDING</abnormalVisualEffect>
		<activateRate>100</activateRate>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<coolTime>800</coolTime>
		<effectPoint>-342</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>3300</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>30</lvlBonusRate>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>82</mpConsume>
		<reuseDelay>2000</reuseDelay>
		<trait>BLEED</trait>
		<effects>
			<effect name="DamOverTime">
				<power>351</power>
				<ticks>1</ticks>
			</effect>
		</effects>
		<endEffects>
			<effect name="CallSkill">
				<skillId>5248</skillId> <!-- Seed Explosion -->
				<skillLevel>9</skillLevel>
			</effect>
		</endEffects>
	</skill>
	<skill id="737" toLevel="1" name="Devil Ultimate Defense">
		<!-- Momentarily and drastically increases resistance to attacks that decreases P. Def./ M. Def. or cancels buffs. The player cannot move while the effect lasts. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>PD_UP_SPECIAL</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>100</effectPoint>
		<hitTime>1000</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>10</mpConsume>
		<reuseDelay>900000</reuseDelay>
		<effects>
			<effect name="BlockMove" />
			<effect name="MagicalDefence">
				<amount>1350</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>1800</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="ResistDispelByCategory">
				<amount>-80</amount>
				<slot>BUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="738" toLevel="3" name="Heretic Heal">
		<!-- Level 1: Instantly recovers the target's HP. Power 892 -->
		<!-- Level 2: Instantly recovers the target's HP. Power 926 -->
		<!-- Level 3: Instantly recovers the target's HP. Power 956 -->
		<icon>icon.skill_transform_etc</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>600</castRange>
		<effectPoint>575</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>5000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>48</mpConsume>
		<mpInitialConsume>12</mpInitialConsume>
		<reuseDelay>1000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">892</value>
					<value level="2">926</value>
					<value level="3">956</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="739" toLevel="3" name="Heretic Battle Heal">
		<!-- Level 1: Immediately restores the target's HP with 743 Power. -->
		<!-- Level 2: Immediately restores the target's HP with 772 Power. -->
		<!-- Level 3: Immediately restores the target's HP with 796 Power. -->
		<icon>icon.skill_transform_etc</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">595</value>
			<value level="2">617</value>
			<value level="3">637</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
			<value level="1">136</value>
			<value level="2">142</value>
			<value level="3">146</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">34</value>
			<value level="2">36</value>
			<value level="3">37</value>
		</mpInitialConsume>
		<reuseDelay>1000</reuseDelay>
		<effects>
			<effect name="Heal">
				<power>
					<value level="1">743</value>
					<value level="2">772</value>
					<value level="3">796</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="740" toLevel="3" name="Heretic Resurrection">
		<!-- Level 1: Resurrects a corpse. Restores XP by about 50% additionally. -->
		<!-- Level 2: Resurrects a corpse. Restores XP by about 55% additionally. -->
		<!-- Level 3: Resurrects a corpse. Restores XP by about 60% additionally. -->
		<icon>icon.skill_transform_etc</icon>
		<operateType>A1</operateType>
		<targetType>PC_BODY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>400</castRange>
		<coolTime>500</coolTime>
		<effectPoint>
			<value level="1">595</value>
			<value level="2">617</value>
			<value level="3">637</value>
		</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>2000</hitTime>
		<isMagic>4</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
			<value level="1">182</value>
			<value level="2">189</value>
			<value level="3">195</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">46</value>
			<value level="2">48</value>
			<value level="3">49</value>
		</mpInitialConsume>
		<reuseDelay>30000</reuseDelay>
		<staticReuse>true</staticReuse>
		<conditions>
			<condition name="OpResurrection" />
		</conditions>
		<effects>
			<effect name="Resurrection">
				<power>
					<value level="1">50</value>
					<value level="2">55</value>
					<value level="3">60</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="741" toLevel="3" name="Heretic Heal Side Effect">
		<!-- Cause side effects of the heal. Momentarily make the enemy addicted. -->
		<icon>icon.skill_transform_debuff</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>8</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>POISON</abnormalType>
		<abnormalVisualEffect>DOT_POISON</abnormalVisualEffect>
		<activateRate>70</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">-323</value>
			<value level="2">-331</value>
			<value level="3">-337</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>4000</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>20</lvlBonusRate>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
			<value level="1">52</value>
			<value level="2">54</value>
			<value level="3">56</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">13</value>
			<value level="2">14</value>
			<value level="3">14</value>
		</mpInitialConsume>
		<reuseDelay>3000</reuseDelay>
		<subordinationAbnormalType>POISON</subordinationAbnormalType>
		<trait>POISON</trait>
		<effects>
			<effect name="DamOverTime">
				<power>52</power>
				<ticks>1</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="742" toLevel="3" name="Veil Master Bursting Flame">
		<!-- Level 1: Attacks the enemy by unleashing dark flames. Power 112 -->
		<!-- Level 2: Attacks the enemy by unleashing dark flames. Power 117 -->
		<!-- Level 3: Attacks the enemy by unleashing dark flames. Power 122 -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>600</castRange>
		<effectPoint>-92</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>1200</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
			<value level="1">68</value>
			<value level="2">70</value>
			<value level="3">73</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">17</value>
			<value level="2">18</value>
			<value level="3">19</value>
		</mpInitialConsume>
		<reuseDelay>2000</reuseDelay>
		<effects>
			<effect name="MagicalDamageRange">
				<power>
					<value level="1">112</value>
					<value level="2">117</value>
					<value level="3">122</value>
				</power>
				<shieldDefPercent>40</shieldDefPercent>
			</effect>
		</effects>
	</skill>
	<skill id="743" toLevel="3" name="Veil Master Dark Explosion">
		<!-- Level 1: Detonates the soul to attack surrounding enemies. Power 56 -->
		<!-- Level 2: Detonates the soul to attack surrounding enemies. Power 59 -->
		<!-- Level 3: Detonates the soul to attack surrounding enemies. Power 61 -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>RANGE</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<castRange>500</castRange>
		<effectPoint>
			<value level="1">-323</value>
			<value level="2">-331</value>
			<value level="3">-337</value>
		</effectPoint>
		<effectRange>1000</effectRange>
		<hitTime>1800</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
			<value level="1">68</value>
			<value level="2">70</value>
			<value level="3">73</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">17</value>
			<value level="2">18</value>
			<value level="3">19</value>
		</mpInitialConsume>
		<reuseDelay>4000</reuseDelay>
		<effects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">56</value>
					<value level="2">59</value>
					<value level="3">61</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="744" toLevel="3" name="Veil Master Dark Flare">
		<!-- Level 1: Attacks a large number of enemies by creating huge explosion. Power 112 -->
		<!-- Level 2: Attacks a large number of enemies by creating huge explosion. Power 117 -->
		<!-- Level 3: Attacks a large number of enemies by creating huge explosion. Power 122 -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>150</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<effectPoint>-275</effectPoint>
		<hitTime>1800</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
			<value level="1">68</value>
			<value level="2">70</value>
			<value level="3">73</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">17</value>
			<value level="2">18</value>
			<value level="3">19</value>
		</mpInitialConsume>
		<reuseDelay>5000</reuseDelay>
		<effects>
			<effect name="MagicalDamage">
				<power>
					<value level="1">112</value>
					<value level="2">117</value>
					<value level="3">122</value>
				</power>
			</effect>
		</effects>
	</skill>
	<skill id="745" toLevel="3" name="Veil Master Dark Cure">
		<!-- Cancel all the debuffs of the target. -->
		<icon>icon.skill_transform_etc</icon>
		<operateType>A1</operateType>
		<targetType>TARGET</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>600</castRange>
		<effectPoint>
			<value level="1">635</value>
			<value level="2">650</value>
			<value level="3">662</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
			<value level="1">21</value>
			<value level="2">22</value>
			<value level="3">23</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">6</value>
			<value level="2">6</value>
			<value level="3">6</value>
		</mpInitialConsume>
		<reuseDelay>8000</reuseDelay>
		<effects>
			<effect name="DispelByCategory">
				<slot>DEBUFF</slot>
				<rate>100</rate>
				<max>10</max>
			</effect>
		</effects>
	</skill>
	<skill id="746" toLevel="3" name="Saber Tooth Tiger Bite">
		<!-- Level 1: Bites ferociously. Power 1789 Over-hit. -->
		<!-- Level 2: Bites ferociously. Power 1955 Over-hit. -->
		<!-- Level 3: Bites ferociously. Power 2117 Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>
			<value level="1">-648</value>
			<value level="2">-660</value>
			<value level="3">-671</value>
		</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>88</mpConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>3000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">1789</value>
					<value level="2">1955</value>
					<value level="3">2117</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="747" toLevel="3" name="Saber Tooth Tiger Fear">
		<!-- Level 1: Attacks surrounding enemies causing them to flee. Power 895 Over-hit. -->
		<!-- Level 2: Attacks surrounding enemies causing them to flee. Power 978 Over-hit. -->
		<!-- Level 3: Attacks surrounding enemies causing them to flee. Power 1059 Over-hit. -->
		<icon>icon.skill_transform_debuff</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>TURN_FLEE</abnormalType>
		<abnormalVisualEffect>TURN_FLEE</abnormalVisualEffect>
		<activateRate>60</activateRate>
		<affectLimit>6-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>150</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<basicProperty>MAGIC</basicProperty>
		<effectPoint>
			<value level="1">-323</value>
			<value level="2">-331</value>
			<value level="3">-337</value>
		</effectPoint>
		<hitTime>2000</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>20</lvlBonusRate>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
			<value level="1">150</value>
			<value level="2">156</value>
			<value level="3">160</value>
		</mpConsume>
		<reuseDelay>15000</reuseDelay>
		<trait>DERANGEMENT</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">895</value>
					<value level="2">978</value>
					<value level="3">1059</value>
				</power>
				<overHit>true</overHit>
			</effect>
			<effect name="BlockControl" />
			<effect name="Fear" />
		</effects>
	</skill>
	<skill id="748" toLevel="1" name="Saber Tooth Tiger Sprint">
		<!-- Temporarily Speed +33. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>SPEED_UP</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>495</effectPoint>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>70</magicLevel>
		<mpConsume>19</mpConsume>
		<mpInitialConsume>5</mpInitialConsume>
		<reuseDelay>3000</reuseDelay>
		<effects>
			<effect name="Speed">
				<amount>33</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="749" toLevel="3" name="Oel Mahum Stun Attack">
		<!-- Level 1: Deals damage and shock simultaneously. Power 895 Momentarily inflicts stun on the enemy. Over-hit. -->
		<!-- Level 2: Deals damage and shock simultaneously. Power 978 Momentarily inflicts stun on the enemy. Over-hit. -->
		<!-- Level 3: Deals damage and shock simultaneously. Power 1059 Momentarily inflicts stun on the enemy. Over-hit. -->
		<icon>icon.skill_transform_debuff</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>9</abnormalTime>
		<abnormalType>STUN</abnormalType>
		<abnormalVisualEffect>STUN</abnormalVisualEffect>
		<activateRate>50</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>
			<value level="1">-323</value>
			<value level="2">-331</value>
			<value level="3">-337</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1080</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>20</lvlBonusRate>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
			<value level="1">68</value>
			<value level="2">71</value>
			<value level="3">73</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>3000</reuseDelay>
		<trait>SHOCK</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">895</value>
					<value level="2">978</value>
					<value level="3">1059</value>
				</power>
				<overHit>true</overHit>
			</effect>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="750" toLevel="1" name="Oel Mahum Ultimate Defense">
		<!-- Momentarily and drastically increases resistance to attacks that decreases P. Def./ M. Def. or cancels buffs. The player cannot move while the effect lasts. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>PD_UP_SPECIAL</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>100</effectPoint>
		<hitTime>1000</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>70</magicLevel>
		<mpConsume>10</mpConsume>
		<reuseDelay>900000</reuseDelay>
		<effects>
			<effect name="BlockMove" />
			<effect name="MagicalDefence">
				<amount>1350</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalDefence">
				<amount>1800</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="ResistDispelByCategory">
				<amount>-80</amount>
				<slot>BUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="751" toLevel="3" name="Oel Mahum Arm Flourish">
		<!-- Level 1: Flourishes weapons to attack surrounding enemies. Power 1073 Over-hit. -->
		<!-- Level 2: Flourishes weapons to attack surrounding enemies. Power 1173 Over-hit. -->
		<!-- Level 3: Flourishes weapons to attack surrounding enemies. Power 1271 Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>80</affectRange>
		<affectScope>FAN</affectScope>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>
			<value level="1">-323</value>
			<value level="2">-331</value>
			<value level="3">-337</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<fanRange>0;0;80;150</fanRange>
		<hitTime>1080</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
			<value level="1">71</value>
			<value level="2">74</value>
			<value level="3">77</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>4000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">1073</value>
					<value level="2">1173</value>
					<value level="3">1271</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="752" toLevel="3" name="Doll Blader Sting">
		<!-- Strikes the enemy making them bleed. Over-hit. -->
		<icon>icon.skill_transform_debuff</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>20</abnormalTime>
		<abnormalType>BLEEDING</abnormalType>
		<abnormalVisualEffect>DOT_BLEEDING</abnormalVisualEffect>
		<activateRate>50</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>
			<value level="1">-323</value>
			<value level="2">-331</value>
			<value level="3">-337</value>
		</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1080</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>20</lvlBonusRate>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
			<value level="1">71</value>
			<value level="2">74</value>
			<value level="3">77</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>3000</reuseDelay>
		<subordinationAbnormalType>BLEEDING</subordinationAbnormalType>
		<trait>BLEED</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">1193</value>
					<value level="2">1303</value>
					<value level="3">1412</value>
				</power>
				<overHit>true</overHit>
			</effect>
			<effect name="DamOverTime">
				<power>134</power>
				<ticks>1</ticks>
			</effect>
		</effects>
	</skill>
	<skill id="753" toLevel="3" name="Doll Blader Throwing Knife">
		<!-- Level 1: Throws a dagger to attack an enemy in the distance. Power 4769 Over-hit. -->
		<!-- Level 2: Throws a dagger to attack an enemy in the distance. Power 5211 Over-hit. -->
		<!-- Level 3: Throws a dagger to attack an enemy in the distance. Power 5645 Over-hit. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectScope>SINGLE</affectScope>
		<castRange>700</castRange>
		<coolTime>800</coolTime>
		<effectPoint>
			<value level="1">-323</value>
			<value level="2">-331</value>
			<value level="3">-337</value>
		</effectPoint>
		<effectRange>1200</effectRange>
		<hitTime>3200</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">70</value>
			<value level="2">73</value>
			<value level="3">76</value>
		</magicLevel>
		<mpConsume>
			<value level="1">142</value>
			<value level="2">148</value>
			<value level="3">153</value>
		</mpConsume>
		<reuseDelay>6000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>
					<value level="1">4769</value>
					<value level="2">5211</value>
					<value level="3">5645</value>
				</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="754" toLevel="1" name="Doll Blader Clairvoyance">
		<!-- Temporarily P. Critical Rate +30%. -->
		<icon>icon.skill_transform_buff</icon>
		<operateType>A2</operateType>
		<targetType>TARGET</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>1200</abnormalTime>
		<abnormalType>CRITICAL_PROB_UP</abnormalType>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<castRange>400</castRange>
		<effectPoint>635</effectPoint>
		<effectRange>900</effectRange>
		<hitTime>4000</hitTime>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>70</magicLevel>
		<mpConsume>52</mpConsume>
		<mpInitialConsume>13</mpInitialConsume>
		<reuseDelay>2000</reuseDelay>
		<effects>
			<effect name="CriticalRate">
				<amount>30</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="762" toLevel="1" name="Insane Crusher">
		<!-- Unleashes a powerful strike with 8409 Power added to P. Atk. and Dark Curse to surrounding enemies when rage reaches its peak. Cancels at least one of the enemy's buffs, and temporarily decreases Max CP significantly. Decreases Debuff Resistance, and effectiveness of receiving HP recovery. Requires a sword or blunt weapon. Ignores shield defense. Critical and Over-hit are possible. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0762</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>60</abnormalTime>
		<abnormalType>TOUCH_OF_DEATH</abnormalType>
		<abnormalVisualEffect>REVENGE_AURA</abnormalVisualEffect>
		<activateRate>90</activateRate>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>300</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<attributeType>DARK</attributeType>
		<attributeValue>20</attributeValue>
		<basicProperty>PHYSICAL</basicProperty>
		<coolTime>700</coolTime>
		<effectPoint>-4100</effectPoint>
		<hitTime>1300</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>20</lvlBonusRate>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>80</mpConsume>
		<reuseDelay>60000</reuseDelay>
		<trait>DEATH</trait>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>SWORD</item>
					<item>BLUNT</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalDamage">
				<power>8409</power>
				<ignoreShieldDefence>true</ignoreShieldDefence>
				<criticalChance>50</criticalChance>
				<overHit>true</overHit>
			</effect>
			<effect name="DispelByCategory">
				<slot>BUFF</slot>
				<rate>25</rate>
				<max>3</max>
			</effect>
			<effect name="MaxCp">
				<amount>-90</amount>
				<mode>PER</mode>
				<heal>true</heal>
			</effect>
			<effect name="HealEffect">
				<amount>-30</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistAbnormalByCategory">
				<amount>30</amount>
				<slot>DEBUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="767" toLevel="10" name="Bow Mastery">
		<!-- Level 1: P. Atk. +25 when using a bow. -->
		<!-- Level 2: P. Atk. +60 when using a bow. -->
		<!-- Level 3: P. Atk. +180 when using a bow. -->
		<!-- Level 4: P. Atk. +315 when using a bow. -->
		<!-- Level 5: P. Atk. +500 when using a bow. -->
		<!-- Level 6: P. Atk. +710 when using a bow. -->
		<!-- Level 7: P. Atk. +800 when using a bow. -->
		<!-- Level 8: P. Atk. +850 when using a bow. -->
		<!-- Level 9: P. Atk. +880 when using a bow. -->
		<!-- Level 10: P. Atk. +920 when using a bow. -->
		<icon>icon.skill6441</icon>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">20</value>
			<value level="2">30</value>
			<value level="3">40</value>
			<value level="4">50</value>
			<value level="5">60</value>
			<value level="6">70</value>
			<value level="7">75</value>
			<value level="8">80</value>
			<value level="9">85</value>
			<value level="10">90</value>
		</magicLevel>
		<effects>
			<effect name="PhysicalAttack">
				<amount>
					<value level="1">25</value>
					<value level="2">60</value>
					<value level="3">180</value>
					<value level="4">315</value>
					<value level="5">500</value>
					<value level="6">710</value>
					<value level="7">800</value>
					<value level="8">850</value>
					<value level="9">880</value>
					<value level="10">920</value>
				</amount>
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</effect>
		</effects>
	</skill>
	<skill id="768" toLevel="1" name="Exciting Adventure">
		<!-- Feels extreme excitement during a melee. For 30 sec., Speed +10, P. Evasion +10, vital points attacks success rate +15%, P. skill Evasion Rate + 40%, and Buff-canceling Attack Resistance +90%. <Auto-use> -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill0768</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>AVOID_UP_SPECIAL</abnormalType>
		<abnormalVisualEffect>ULTIMATE_DEFENCE</abnormalVisualEffect>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>679</effectPoint>
		<hitTime>1000</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>36</mpConsume>
		<reuseDelay>900000</reuseDelay>
		<effects>
			<effect name="SkillEvasion">
				<magicType>0</magicType>
				<amount>40</amount>
			</effect>
			<effect name="Speed">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalEvasion">
				<amount>10</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="FatalBlowRate">
				<amount>15</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistDispelByCategory">
				<amount>-90</amount>
				<slot>BUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="769" toLevel="1" name="Wind Riding">
		<!-- Storms the battleground riding the Wind. For 30 sec., Speed +30, Evasion +15, vital points attacks success rate +10%, physical skill Evasion Rate +60%, and Buff-canceling Attack Resistance +80%. <Auto-use> -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill0769</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>AVOID_UP_SPECIAL</abnormalType>
		<abnormalVisualEffect>ULTIMATE_DEFENCE</abnormalVisualEffect>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>679</effectPoint>
		<hitTime>1000</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>36</mpConsume>
		<reuseDelay>900000</reuseDelay>
		<effects>
			<effect name="SkillEvasion">
				<magicType>0</magicType>
				<amount>60</amount>
			</effect>
			<effect name="Speed">
				<amount>30</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalEvasion">
				<amount>15</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="FatalBlowRate">
				<amount>10</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistDispelByCategory">
				<amount>-80</amount>
				<slot>BUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="770" toLevel="1" name="Ghost Walking">
		<!-- Infiltrates the battleground like a ghost. For 30 sec., Speed +20, P. Evasion +10, vital points attacks success rate +20%, P. Skills Evasion +50%, and Buff-canceling Attack Resistance +80%. <Auto-use> -->
		<shortcutToggleType>1</shortcutToggleType>
		<icon>icon.skill0770</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>2</abnormalLevel>
		<abnormalTime>30</abnormalTime>
		<abnormalType>AVOID_UP_SPECIAL</abnormalType>
		<abnormalVisualEffect>ULTIMATE_DEFENCE</abnormalVisualEffect>
		<affectScope>SINGLE</affectScope>
		<basicProperty>NONE</basicProperty>
		<effectPoint>679</effectPoint>
		<hitTime>1000</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>36</mpConsume>
		<reuseDelay>900000</reuseDelay>
		<effects>
			<effect name="SkillEvasion">
				<magicType>0</magicType>
				<amount>10</amount>
			</effect>
			<effect name="Speed">
				<amount>20</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="PhysicalEvasion">
				<amount>50</amount>
				<mode>DIFF</mode>
			</effect>
			<effect name="FatalBlowRate">
				<amount>20</amount>
				<mode>PER</mode>
			</effect>
			<effect name="ResistDispelByCategory">
				<amount>-80</amount>
				<slot>BUFF</slot>
			</effect>
		</effects>
	</skill>
	<skill id="771" toLevel="1" name="Flame Hawk">
		<!-- Fires an arrow imbued with the phoenix energy, attacking the target and enemies at the front with 8049 power. For 10 sec., inflicts 250 additional damage per sec. Ignores 10% of the target's defense. Requires a bow. Critical. Over-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0771</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>900</affectRange>
		<affectScope>FAN</affectScope>
		<castRange>900</castRange>
		<coolTime>1000</coolTime>
		<effectPoint>-500</effectPoint>
		<effectRange>1400</effectRange>
		<fanRange>0;0;900;40</fanRange>
		<hitTime>3500</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>280</mpConsume>
		<reuseDelay>30000</reuseDelay>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalDamage">
				<power>8049</power>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
				<pDefMod>0.9</pDefMod>
			</effect>
			<effect name="CallSkill">
				<skillId>23298</skillId> <!-- Flame Hawk -->
				<skillLevel>1</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="772" toLevel="1" name="Arrow Rain">
		<!-- Makes arrows rain down from the sky, attacking the target and nearby enemies with 7043 power. For 10 sec., inflicts 250 additional damage per sec. Ignores 10% of the target's defense. Requires a bow. Critical. Over-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0772</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>RANGE</affectScope>
		<castRange>900</castRange>
		<coolTime>1000</coolTime>
		<effectPoint>-500</effectPoint>
		<effectRange>1400</effectRange>
		<hitTime>4000</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>280</mpConsume>
		<reuseDelay>30000</reuseDelay>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalDamage">
				<power>7043</power>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
				<pDefMod>0.9</pDefMod>
			</effect>
			<effect name="CallSkill">
				<skillId>23299</skillId> <!-- Arrow Rain -->
				<skillLevel>1</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="773" toLevel="1" name="Ghost Piercing">
		<!-- Shoots ghost arrows, attacking the target and enemies in the front with 8452 power. For 10 sec., inflicts 300 additional damage per sec. Ignores 5% of the target's defense. Requires a bow. Critical. Over-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0773</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>900</affectRange>
		<affectScope>SQUARE</affectScope>
		<castRange>900</castRange>
		<coolTime>1000</coolTime>
		<effectPoint>-500</effectPoint>
		<effectRange>1400</effectRange>
		<fanRange>0;0;900;100</fanRange>
		<hitTime>3000</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>280</mpConsume>
		<reuseDelay>30000</reuseDelay>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>BOW</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalDamage">
				<power>8452</power>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
				<pDefMod>0.9</pDefMod>
			</effect>
			<effect name="CallSkill">
				<skillId>23300</skillId> <!-- Ghost Piercing -->
				<skillLevel>1</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="777" toLevel="1" name="Demolition Impact">
		<!-- Unleashes a destructive impulse wave to attack the target with 5980 power added to P. Atk. For 10 sec., P./ M. Def. -30%. Requires a sword or a blunt weapon. Critical, over-hit. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0777</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>ULTIMATE_DEBUFF</abnormalType>
		<activateRate>60</activateRate>
		<affectScope>SINGLE</affectScope>
		<castRange>150</castRange>
		<effectPoint>-686</effectPoint>
		<effectRange>200</effectRange>
		<hitTime>1200</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>20</lvlBonusRate>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>240</mpConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>75000</reuseDelay>
		<conditions>
			<condition name="EquipWeapon">
				<weaponType>
					<item>SWORD</item>
					<item>BLUNT</item>
				</weaponType>
			</condition>
		</conditions>
		<effects>
			<effect name="PhysicalDamage">
				<power>5980</power>
				<overHit>true</overHit>
				<criticalChance>15</criticalChance>
			</effect>
			<effect name="PhysicalDefence">
				<amount>-30</amount>
				<mode>PER</mode>
			</effect>
			<effect name="MagicalDefence">
				<amount>-30</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="790" toLevel="3" name="Critical Shot">
		<!-- Level 1: P. Critical Rate +10 when using a bow. -->
		<!-- Level 2: P. Critical Rate +20 when using a bow. -->
		<!-- Level 3: P. Critical Rate +30 when using a bow. -->
		<icon>icon.skill11874</icon>
		<operateType>P</operateType>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">52</value>
			<value level="2">66</value>
			<value level="3">80</value>
		</magicLevel>
		<effects>
			<effect name="TriggerSkillByMagicType">
				<magicTypes>0</magicTypes>
				<skillId>794</skillId> <!-- Enhanced Critical Shot -->
				<skillLevel>1</skillLevel>
				<chance>10</chance>
				<targetType>SELF</targetType>
			</effect>
			<effect name="TriggerSkillByDamageDealt">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>10</chance>
				<targetType>SELF</targetType>
				<isCritical>true</isCritical>
				<allowWeapons>BOW</allowWeapons>
				<skillId>794</skillId> <!-- Enhanced Critical Shot -->
				<skillLevel>1</skillLevel>
			</effect>
			<effect name="TriggerSkillByDamageDealt">
				<attackerType>Creature</attackerType>
				<minDamage>1</minDamage>
				<chance>10</chance>
				<targetType>SELF</targetType>
				<isCritical>false</isCritical>
				<allowWeapons>BOW</allowWeapons>
				<skillId>794</skillId> <!-- Enhanced Critical Shot -->
				<skillLevel>1</skillLevel>
			</effect>
		</effects>
	</skill>
	<skill id="791" toLevel="5" name="Lightning">
		<!-- Strikes the target with lightning paralyzing them for 3 sec. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0791</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>1</abnormalLevel>
		<abnormalTime>3</abnormalTime>
		<abnormalType>PARALYZE</abnormalType>
		<abnormalVisualEffect>PARALYZE</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>MAGIC</basicProperty>
		<coolTime>720</coolTime>
		<effectPoint>
			<value level="1">-460</value>
			<value level="2">-470</value>
			<value level="3">-480</value>
			<value level="4">-490</value>
			<value level="5">-500</value>
		</effectPoint>
		<hitTime>1080</hitTime>
		<isDebuff>true</isDebuff>
		<isMagic>1</isMagic> <!-- Magic Skill -->
		<lvlBonusRate>30</lvlBonusRate>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">76</value>
			<value level="2">78</value>
			<value level="3">80</value>
			<value level="4">82</value>
			<value level="5">84</value>
		</magicLevel>
		<mpConsume>
			<value level="1">20</value>
			<value level="2">23</value>
			<value level="3">25</value>
			<value level="4">28</value>
			<value level="5">30</value>
		</mpConsume>
		<mpInitialConsume>
			<value level="1">10</value>
			<value level="2">12</value>
			<value level="3">15</value>
			<value level="4">17</value>
			<value level="5">20</value>
		</mpInitialConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>20000</reuseDelay>
		<trait>PARALYZE</trait>
		<effects>
			<effect name="BlockActions">
				<allowedSkills>10279;10517;11264;11093;1904;1912;13314;13542;30010;30018;30516;461;35016;35045</allowedSkills>
			</effect>
		</effects>
	</skill>
	<skill id="792" toLevel="5" name="Soul Stigma">
		<!-- Level 1: Marks the target. For 10 sec., target's P./ M. Critical Damage -20%. -->
		<!-- Level 2: Marks the target. For 10 sec., target's P./ M. Critical Damage -25%. -->
		<!-- Level 3: Marks the target. For 10 sec., target's P./ M. Critical Damage -30%. -->
		<!-- Level 4: Marks the target. For 10 sec., target's P./ M. Critical Damage -35%. -->
		<!-- Level 5: Marks the target. For 10 sec., target's P./ M. Critical Damage -40%. -->
		<shortcutToggleType>2</shortcutToggleType>
		<icon>icon.skill0792</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY_ONLY</targetType>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
			<value level="4">4</value>
			<value level="5">5</value>
		</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>ARCHER_DEBUFF2</abnormalType>
		<abnormalVisualEffect>DOT_POISON</abnormalVisualEffect>
		<activateRate>80</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>900</castRange>
		<coolTime>300</coolTime>
		<effectPoint>
			<value level="1">-460</value>
			<value level="2">-470</value>
			<value level="3">-480</value>
			<value level="4">-490</value>
			<value level="5">-500</value>
		</effectPoint>
		<effectRange>1100</effectRange>
		<hitTime>1500</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>20</lvlBonusRate>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">76</value>
			<value level="2">78</value>
			<value level="3">80</value>
			<value level="4">82</value>
			<value level="5">84</value>
		</magicLevel>
		<mpConsume>
			<value level="1">40</value>
			<value level="2">45</value>
			<value level="3">50</value>
			<value level="4">55</value>
			<value level="5">60</value>
		</mpConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>15000</reuseDelay>
		<trait>BOSS</trait>
		<effects>
			<effect name="MagicCriticalDamage">
				<amount>
					<value level="1">-20</value>
					<value level="2">-25</value>
					<value level="3">-30</value>
					<value level="4">-35</value>
					<value level="5">-40</value>
				</amount>
				<mode>PER</mode>
			</effect>
			<effect name="CriticalDamage">
				<amount>
					<value level="1">-20</value>
					<value level="2">-25</value>
					<value level="3">-30</value>
					<value level="4">-35</value>
					<value level="5">-40</value>
				</amount>
				<mode>PER</mode>
			</effect>
		</effects>
	</skill>
	<skill id="794" toLevel="3" name="Enhanced Critical Shot">
		<!-- Level 1: P. Critical Rate +10. -->
		<!-- Level 2: P. Critical Rate +20. -->
		<!-- Level 3: P. Critical Rate +30. -->
		<icon>icon.skill11874</icon>
		<operateType>A2</operateType>
		<targetType>SELF</targetType>
		<abnormalLevel>
			<value level="1">1</value>
			<value level="2">2</value>
			<value level="3">3</value>
		</abnormalLevel>
		<abnormalTime>10</abnormalTime>
		<abnormalType>ARCHER_BUFF3</abnormalType>
		<affectObject>FRIEND</affectObject>
		<affectScope>SINGLE</affectScope>
		<isMagic>2</isMagic>
		<isTriggeredSkill>true</isTriggeredSkill>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>
			<value level="1">52</value>
			<value level="2">66</value>
			<value level="3">80</value>
		</magicLevel>
		<reuseDelay>500</reuseDelay>
		<effects>
			<effect name="CriticalRate">
				<amount>
					<value level="1">10</value>
					<value level="2">20</value>
					<value level="3">30</value>
				</amount>
				<mode>DIFF</mode>
			</effect>
		</effects>
	</skill>
	<skill id="795" toLevel="1" name="Divine Knight Brandish">
		<!-- Attacks the enemies in front by flourishing an ax. Over-hit is possible. Power 2322. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>FAN</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>400</effectRange>
		<fanRange>0;0;200;180</fanRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>88</mpConsume>
		<reuseDelay>3000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>2322</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="796" toLevel="1" name="Divine Knight Explosion Attack">
		<!-- Detonates the floor by striking it hard. Power 1900. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>SELF</targetType>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>200</affectRange>
		<affectScope>POINT_BLANK</affectScope>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<hitTime>830</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>88</mpConsume>
		<reuseDelay>3000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>1900</power>
			</effect>
		</effects>
	</skill>
	<skill id="797" toLevel="1" name="Divine Rogue Piercing Attack">
		<!-- Attacks all enemies, even the enemies behind you, with a strong thrust. Over-hit is possible. Power 2111. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A1</operateType>
		<targetType>ENEMY</targetType>
		<affectLimit>5-12</affectLimit>
		<affectObject>NOT_FRIEND</affectObject>
		<affectRange>250</affectRange>
		<affectScope>SQUARE</affectScope>
		<castRange>40</castRange>
		<coolTime>660</coolTime>
		<effectPoint>-100</effectPoint>
		<effectRange>200</effectRange>
		<fanRange>0;0;250;60</fanRange>
		<hitTime>830</hitTime>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>88</mpConsume>
		<reuseDelay>3000</reuseDelay>
		<effects>
			<effect name="PhysicalDamage">
				<power>2111</power>
				<overHit>true</overHit>
			</effect>
		</effects>
	</skill>
	<skill id="798" toLevel="1" name="Divine Warrior Crippling Attack">
		<!-- Poisons the enemy with an unexpected poison attack. -->
		<icon>icon.skill_transform_s_attack</icon>
		<operateType>A2</operateType>
		<targetType>ENEMY</targetType>
		<abnormalLevel>3</abnormalLevel>
		<abnormalTime>20</abnormalTime>
		<abnormalType>POISON</abnormalType>
		<abnormalVisualEffect>DOT_BLEEDING</abnormalVisualEffect>
		<activateRate>50</activateRate>
		<affectScope>SINGLE</affectScope>
		<basicProperty>PHYSICAL</basicProperty>
		<castRange>40</castRange>
		<coolTime>720</coolTime>
		<effectPoint>-340</effectPoint>
		<effectRange>400</effectRange>
		<hitTime>1080</hitTime>
		<isDebuff>true</isDebuff>
		<lvlBonusRate>20</lvlBonusRate>
		<magicCriticalRate>5</magicCriticalRate>
		<magicLevel>80</magicLevel>
		<mpConsume>88</mpConsume>
		<nextAction>ATTACK</nextAction>
		<reuseDelay>3000</reuseDelay>
		<subordinationAbnormalType>POISON</subordinationAbnormalType>
		<trait>POISON</trait>
		<effects>
			<effect name="PhysicalDamage">
				<power>1689</power>
			</effect>
			<effect name="DamOverTime">
				<power>69</power>
				<ticks>1</ticks>
			</effect>
		</effects>
	</skill>
</list>
