<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
	<xs:element name="list">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="object" maxOccurs="unbounded" minOccurs="1">
					<xs:complexType>
						<xs:simpleContent>
							<xs:extension base="xs:string">
								<xs:attribute name="id" type="xs:integer" use="required" />
								<xs:attribute name="name" type="xs:string" use="required" />
								<xs:attribute name="type" type="xs:byte" use="required" />
								<xs:attribute name="x" type="xs:integer" use="required" />
								<xs:attribute name="y" type="xs:integer" use="required" />
								<xs:attribute name="z" type="xs:integer" use="required" />
								<xs:attribute name="texture" type="xs:string" />
								<xs:attribute name="map_x" type="xs:integer" />
								<xs:attribute name="map_y" type="xs:integer" />
							</xs:extension>
						</xs:simpleContent>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>